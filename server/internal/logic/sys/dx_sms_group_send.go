// Package sys
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2024 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
// @AutoGenerate Version 2.15.11
package sys

import (
	"bufio"
	"context"
	"errors"
	"fmt"
	"hotgo/internal/dao"
	"hotgo/internal/library/contexts"
	"hotgo/internal/library/hgorm"
	"hotgo/internal/library/hgorm/handler"
	"hotgo/internal/library/hgorm/hook"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/sysin"
	"hotgo/internal/service"
	"hotgo/utility/url"
	"os"
	"regexp"
	"strings"
	"time"

	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gtime"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/tealeg/xlsx"
)

type sSysDxSmsGroupSend struct{}

func NewSysDxSmsGroupSend() *sSysDxSmsGroupSend {
	return &sSysDxSmsGroupSend{}
}

func init() {
	service.RegisterSysDxSmsGroupSend(NewSysDxSmsGroupSend())
}

// Model 短信群发ORM模型
func (s *sSysDxSmsGroupSend) Model(ctx context.Context, option ...*handler.Option) *gdb.Model {
	return handler.Model(dao.DxSmsGroupSend.Ctx(ctx), option...)
}

// List 获取短信群发列表
func (s *sSysDxSmsGroupSend) List(ctx context.Context, in *sysin.DxSmsGroupSendListInp) (list []*sysin.DxSmsGroupSendListModel, totalCount int, err error) {
	mod := s.Model(ctx)

	// 字段过滤
	mod = mod.FieldsPrefix(dao.DxSmsGroupSend.Table(), sysin.DxSmsGroupSendListModel{})
	mod = mod.Fields(hgorm.JoinFields(ctx, sysin.DxSmsGroupSendListModel{}, &dao.DxSmsSign, "dxSmsSign"))
	mod = mod.Fields(hgorm.JoinFields(ctx, sysin.DxSmsGroupSendListModel{}, &dao.DxSmsGroupTemplate, "dxSmsGroupTemplate"))
	mod = mod.Fields(hgorm.JoinFields(ctx, sysin.DxSmsSignListModel{}, &dao.AdminMember, "adminMember"))

	// 关联表字段
	mod = mod.LeftJoinOnFields(dao.DxSmsSign.Table(), dao.DxSmsGroupSend.Columns().SignId, "=", dao.DxSmsSign.Columns().Id)
	mod = mod.LeftJoinOnFields(dao.DxSmsGroupTemplate.Table(), dao.DxSmsGroupSend.Columns().TemplateId, "=", dao.DxSmsGroupTemplate.Columns().Id)
	mod = mod.LeftJoinOnFields(dao.AdminMember.Table(), dao.DxSmsSign.Columns().MemberId, "=", dao.AdminMember.Columns().Id)

	// 查询ID
	if in.Id > 0 {
		mod = mod.Where(dao.DxSmsGroupSend.Columns().Id, in.Id)
	}

	// 查询批次号
	if in.BatchNo != "" {
		mod = mod.WhereLike(dao.DxSmsGroupSend.Columns().BatchNo, "%"+in.BatchNo+"%")
	}

	// 查询短信模式
	if in.SmsMode > 0 {
		mod = mod.Where(dao.DxSmsGroupSend.Columns().SmsMode, in.SmsMode)
	}

	// 查询发送模式
	if in.SendMode > 0 {
		mod = mod.Where(dao.DxSmsGroupSend.Columns().SendMode, in.SendMode)
	}

	// 查询发送状态
	if in.SendStatus > 0 {
		mod = mod.Where(dao.DxSmsGroupSend.Columns().SendStatus, in.SendStatus)
	}

	// 查询审核状态
	if in.AuditStatus > 0 {
		mod = mod.Where(dao.DxSmsGroupSend.Columns().AuditStatus, in.AuditStatus)
	}

	// 查询创建时间
	if len(in.CreatedAt) == 2 {
		mod = mod.WhereBetween(dao.DxSmsGroupSend.Columns().CreatedAt, in.CreatedAt[0], in.CreatedAt[1])
	}

	// 查询签名文本
	if in.DxSmsSignSignText != "" {
		mod = mod.WherePrefixLike(dao.DxSmsSign.Table(), dao.DxSmsSign.Columns().SignText, "%"+in.DxSmsSignSignText+"%")
	}

	// 查询模板名称
	if in.DxSmsGroupTemplateTplName != "" {
		mod = mod.WherePrefixLike(dao.DxSmsGroupTemplate.Table(), dao.DxSmsGroupTemplate.Columns().TplName, "%"+in.DxSmsGroupTemplateTplName+"%")
	}

	// 查询创建者
	// if in.Member != "" {
	// 	ids, err := service.AdminMember().GetIdsByKeyword(ctx, in.Member)
	// 	if err != nil {
	// 		return nil, 0, err
	// 	}
	// 	mod = mod.WhereIn(dao.DxSmsAuditLog.Columns().CreatedBy, ids)
	// }

	// 查询企业名称
	if in.AdminMemberRealName != "" {
		mod = mod.WherePrefixLike(dao.AdminMember.Table(), dao.AdminMember.Columns().RealName, "%"+in.AdminMemberRealName+"%")
	}

	// 查询子帐号
	if in.AdminMemberUsername != "" {
		mod = mod.WherePrefixLike(dao.AdminMember.Table(), dao.AdminMember.Columns().Username, "%"+in.AdminMemberUsername+"%")
	}

	// 分页
	mod = mod.Page(in.Page, in.PerPage)

	// 排序
	mod = mod.OrderDesc(dao.DxSmsGroupSend.Table() + "." + dao.DxSmsGroupSend.Columns().Id)

	// 操作人摘要信息
	mod = mod.Hook(hook.MemberSummary)

	// 查询数据
	if err = mod.ScanAndCount(&list, &totalCount, false); err != nil {
		err = gerror.Wrap(err, "获取短信群发列表失败，请稍后重试！")
		return
	}
	return
}

// Edit 修改/新增短信群发
func (s *sSysDxSmsGroupSend) Edit(ctx context.Context, in *sysin.DxSmsGroupSendEditInp) (err error) {
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {

		// 修改
		if in.Id > 0 {
			var res *sysin.DxSmsGroupSendViewModel
			if res, err = s.View(ctx, &sysin.DxSmsGroupSendViewInp{Id: in.Id}); err != nil {
				err = gerror.Wrap(err, "修改短信群发失败，未获取到短信群发信息！")
				return
			} else {
				if res.AuditStatus == 2 || res.AuditStatus == 3 {
					err = gerror.New("已审核通过的短信群发不允许修改！")
					return
				}
			}

			// 处理并去重手机号码
			if in.Mobiles != "" {
				deduplicatedMobiles, validCount, _ := processAndDeduplicateMobiles(in.Mobiles)
				in.Mobiles = deduplicatedMobiles

				// 用原来的MobileNum减去重复的数量
				in.MobileNum = validCount
			}

			if _, err = s.Model(ctx).
				Fields(sysin.DxSmsGroupSendUpdateFields{}).
				WherePri(in.Id).Data(in).Update(); err != nil {
				err = gerror.Wrap(err, "修改短信群发失败，请稍后重试！")
			}
			return
		}

		// 新增
		if in.MemberId < 1 {
			in.MemberId = contexts.GetUserId(ctx)
		}

		// 验证定时发送时间
		if in.SendMode == 2 { // 定时发送模式
			if in.ScheduleTime == nil || in.ScheduleTime.IsZero() {
				err = gerror.New("定时发送模式下，定时发送时间不能为空")
				return
			}

			// 判断定时发送时间是否早于或等于当前时间
			if !in.ScheduleTime.After(gtime.Now()) {
				err = gerror.New("定时发送时间必须大于当前时间")
				return
			}
		}

		// 生成批次号
		nano := time.Now().UnixMicro()
		if in.SmsMode == 1 {
			in.BatchNo = fmt.Sprintf("GS_%d", nano)
		} else if in.SmsMode == 2 {
			in.BatchNo = fmt.Sprintf("TS_%d", nano)
		}

		// 获取用户扩展信息
		var company *sysin.AdminCompanyViewModel
		if err = dao.AdminCompany.Ctx(ctx).
			Where("member_id", in.MemberId).Scan(&company); err != nil {
			err = gerror.Wrap(err, "创建短信群发失败，请稍后重试！")
			return
		}

		// 获取短信余额
		balance := company.SmsBalance - company.SmsFrozen

		// 判断短信余额是否充足
		if balance <= 0 {
			err = gerror.Wrap(errors.New("短信余额不足"), "创建短信群发失败，请联系管理员充值！")
			return
		}

		// 设置已配置的短信通道
		if company.ChannelId < 1 {
			err = gerror.Wrap(errors.New("未配置用户短信通道"), "创建短信群发失败，请联系管理员配置短信通道！")
			return
		} else {
			in.ChannelId = company.ChannelId
		}

		// 设置已配置的短信类型
		if company.SmsType < 1 {
			err = gerror.Wrap(errors.New("未配置用户短信类型"), "创建短信群发失败，请联系管理员配置短信类型！")
			return
		}

		// 处理并去重手机号码
		if in.Mobiles != "" {
			deduplicatedMobiles, _, duplicateCount := processAndDeduplicateMobiles(in.Mobiles)
			in.Mobiles = deduplicatedMobiles

			// 用原来的MobileNum减去重复的数量
			in.MobileNum = in.MobileNum - duplicateCount
		}

		// 判断短信余额是否充足
		if in.MobileNum > balance {
			err = gerror.Wrap(errors.New("短信余额不足"), "短信余额不足，请联系管理员充值！")
			return
		}

		// 判断是否需要审核,设置审核状态
		if company.GroupSendAuditMode == 2 {
			in.AuditStatus = 3 // 免审
		} else {
			in.AuditStatus = 1 // 等待审核
			// 发送审核通知
			if err = service.SysNotifyConfig().SendAuditNotify(ctx, 3); err != nil {
				g.Log().Errorf(ctx, "发送审核通知失败：%+v", err)
				err = gerror.Wrap(err, "新增短信群发失败，请稍后重试！")
				return
			}
		}

		if _, err = s.Model(ctx, &handler.Option{FilterAuth: false}).
			Fields(sysin.DxSmsGroupSendInsertFields{}).
			Data(in).OmitEmptyData().InsertAndGetId(); err != nil {
			err = gerror.Wrap(err, "新增短信群发失败，请稍后重试！")
			return
		}

		// 通讯录分组信息已经通过 ContactGroups 字段保存到数据库
		// 手机号码的展开将在实际发送短信时处理

		return
	})
}

// Delete 删除短信群发
func (s *sSysDxSmsGroupSend) Delete(ctx context.Context, in *sysin.DxSmsGroupSendDeleteInp) (err error) {

	if _, err = s.Model(ctx).WherePri(in.Id).Unscoped().Delete(); err != nil {
		err = gerror.Wrap(err, "删除短信群发失败，请稍后重试！")
		return
	}
	return
}

// View 获取短信群发指定信息
func (s *sSysDxSmsGroupSend) View(ctx context.Context, in *sysin.DxSmsGroupSendViewInp) (res *sysin.DxSmsGroupSendViewModel, err error) {
	mod := s.Model(ctx)

	// 字段过滤
	mod = mod.FieldsPrefix(dao.DxSmsGroupSend.Table(), sysin.DxSmsGroupSendViewModel{})
	mod = mod.Fields(hgorm.JoinFields(ctx, sysin.DxSmsGroupSendViewModel{}, &dao.DxSmsSign, "dxSmsSign"))
	mod = mod.Fields(hgorm.JoinFields(ctx, sysin.DxSmsGroupSendViewModel{}, &dao.DxSmsGroupTemplate, "dxSmsGroupTemplate"))
	if contexts.IsCompanyDept(ctx) {
		mod = mod.Fields(hgorm.JoinFields(ctx, sysin.DxSmsGroupSendViewModel{}, &dao.SysSmsChannel, "sysSmsChannel"))
	}

	// 关联表字段
	mod = mod.LeftJoinOnFields(dao.DxSmsSign.Table(), dao.DxSmsGroupSend.Columns().SignId, "=", dao.DxSmsSign.Columns().Id)
	mod = mod.LeftJoinOnFields(dao.DxSmsGroupTemplate.Table(), dao.DxSmsGroupSend.Columns().TemplateId, "=", dao.DxSmsGroupTemplate.Columns().Id)
	if contexts.IsCompanyDept(ctx) {
		mod = mod.LeftJoinOnFields(dao.SysSmsChannel.Table(), dao.DxSmsGroupSend.Columns().ChannelId, "=", dao.SysSmsChannel.Columns().Id)
	}

	// 操作人摘要信息
	mod = mod.Hook(hook.MemberSummary)

	if err = mod.WherePri(in.Id).Scan(&res); err != nil {
		err = gerror.Wrap(err, "获取短信群发信息，请稍后重试！")
		return
	}

	// 将文件路径转为可下载的网络路径
	if res.FileUrl != "" {
		res.FileUrl = url.GetAddr(ctx) + "/" + res.FileUrl
	}
	return
}

// isValidPhone 验证手机号格式
func isValidPhone(phone string) bool {
	reg := `^1[3-9]\d{9}$`
	match, _ := regexp.MatchString(reg, phone)
	return match
}

// processAndDeduplicateMobiles 处理并去重手机号码
func processAndDeduplicateMobiles(mobiles string) (string, int, int) {
	if mobiles == "" {
		return "", 0, 0
	}

	// 分割手机号码，支持逗号、分号、换行符等分隔符
	separators := []string{",", ";", "\n", "\r\n", "\r", "\t", " "}
	mobileList := []string{mobiles}

	// 逐个分隔符进行分割
	for _, sep := range separators {
		var newList []string
		for _, item := range mobileList {
			parts := strings.Split(item, sep)
			newList = append(newList, parts...)
		}
		mobileList = newList
	}

	// 统计原始有效号码数量和去重后的号码
	uniqueMobiles := make(map[string]bool)
	var validMobiles []string
	originalValidCount := 0

	for _, mobile := range mobileList {
		mobile = strings.TrimSpace(mobile)
		if mobile == "" {
			continue
		}

		// 验证手机号格式
		if !isValidPhone(mobile) {
			continue
		}

		originalValidCount++ // 统计原始有效号码数量

		// 去重
		if !uniqueMobiles[mobile] {
			uniqueMobiles[mobile] = true
			validMobiles = append(validMobiles, mobile)
		}
	}

	// 计算重复数量
	duplicateCount := originalValidCount - len(validMobiles)

	// 返回去重后的手机号码字符串、去重后数量、重复数量
	return strings.Join(validMobiles, ","), len(validMobiles), duplicateCount
}

// CheckFile 检查短信群发文件
func (s *sSysDxSmsGroupSend) CheckFile(ctx context.Context, in *sysin.DxSmsGroupSendCheckFileInp) (res *sysin.DxSmsGroupSendCheckFileModel, err error) {
	res = new(sysin.DxSmsGroupSendCheckFileModel)

	// 获取文件完整路径
	fullPath := gfile.Pwd() + "/resource/public/" + in.FileUrl
	if !gfile.Exists(fullPath) {
		err = gerror.New("文件不存在")
		return
	}

	validMobiles := make(map[string]bool, 100000) // 预分配更大空间
	invalidMobiles := make([]string, 0, 1000)     // 预分配空间

	// 生成新文件路径
	fileDir := strings.ReplaceAll(gfile.Dir(in.FileUrl), "\\", "/")
	fileName := fmt.Sprintf("checked_%s.%s", gfile.Name(in.FileUrl), gfile.ExtName(in.FileUrl))
	newFileUrl := fileDir + "/" + fileName
	newFilePath := gfile.Pwd() + "/resource/public/" + newFileUrl

	// 确保目录存在
	if err = gfile.Mkdir(gfile.Dir(newFilePath)); err != nil {
		return nil, gerror.Wrap(err, "创建目录失败")
	}

	if in.TemplateId > 0 {
		// 处理Excel模板文件
		var template *entity.DxSmsGroupTemplate
		if err = dao.DxSmsGroupTemplate.Ctx(ctx).Where("id", in.TemplateId).Scan(&template); err != nil {
			return nil, gerror.Wrap(err, "获取模板信息失败")
		}
		if template == nil {
			return nil, gerror.New("模板不存在")
		}

		// 读取Excel文件
		xlFile, err := xlsx.OpenFile(fullPath)
		if err != nil {
			return nil, gerror.Wrap(err, "打开Excel文件失败")
		}

		// 获取第一个sheet
		if len(xlFile.Sheets) == 0 {
			return nil, gerror.New("Excel文件中没有工作表")
		}
		sheet := xlFile.Sheets[0]

		// 创建新的Excel文件
		newXlFile := xlsx.NewFile()
		newSheet, err := newXlFile.AddSheet("Sheet1")
		if err != nil {
			return nil, gerror.Wrap(err, "创建Excel工作表失败")
		}

		// 复制表头
		if len(sheet.Rows) > 0 {
			headerRow := sheet.Rows[0]
			newHeaderRow := newSheet.AddRow()
			for _, cell := range headerRow.Cells {
				newCell := newHeaderRow.AddCell()
				newCell.Value = cell.String()
			}
		}

		// 处理数据行
		for i := 1; i < len(sheet.Rows); i++ {
			row := sheet.Rows[i]
			if len(row.Cells) == 0 {
				continue // 跳过空行
			}

			// 确保至少有一个单元格
			if len(row.Cells) == 0 || row.Cells[0].String() == "" {
				continue // 跳过没有手机号的行
			}

			mobile := strings.TrimSpace(row.Cells[0].String())

			// 验证手机号格式
			if !isValidPhone(mobile) {
				invalidMobiles = append(invalidMobiles, mobile)
				res.FailCount++
				continue
			}

			// 检查是否重复
			if _, exists := validMobiles[mobile]; exists {
				res.DuplicateCount++
				continue
			}

			// 验证变量个数是否匹配模板要求
			if len(row.Cells)-1 != template.VarNum {
				invalidMobiles = append(invalidMobiles, mobile)
				res.FailCount++
				continue
			}

			// 添加有效的手机号到集合
			validMobiles[mobile] = true
			res.SuccessCount++

			// 将整行数据写入新文件，保留所有列
			newRow := newSheet.AddRow()
			for _, cell := range row.Cells {
				newCell := newRow.AddCell()
				newCell.Value = cell.String()
			}
		}

		// 保存新的Excel文件
		if err = newXlFile.Save(newFilePath); err != nil {
			return nil, gerror.Wrap(err, "保存Excel文件失败")
		}

		res.NewFileUrl = newFileUrl
	} else {
		// 处理txt文件 - 使用更高效的方式
		file, err := os.Open(fullPath)
		if err != nil {
			return nil, gerror.Wrap(err, "打开文件失败")
		}
		defer file.Close()

		// 创建输出文件
		outFile, err := os.Create(newFilePath)
		if err != nil {
			return nil, gerror.Wrap(err, "创建文件失败")
		}
		defer outFile.Close()

		// 使用更大的缓冲区
		const maxCapacity = 8 * 1024 * 1024 // 8MB
		scanner := bufio.NewScanner(file)
		buf := make([]byte, maxCapacity)
		scanner.Buffer(buf, maxCapacity)

		writer := bufio.NewWriterSize(outFile, maxCapacity)
		defer writer.Flush()

		// 批量处理
		const flushBatchSize = 20000
		batchCount := 0

		for scanner.Scan() {
			mobile := strings.TrimSpace(scanner.Text())
			if mobile == "" {
				continue
			}

			// 验证手机号格式
			if !isValidPhone(mobile) {
				invalidMobiles = append(invalidMobiles, mobile)
				res.FailCount++
				continue
			}

			if _, exists := validMobiles[mobile]; exists {
				res.DuplicateCount++
				continue
			}

			validMobiles[mobile] = true
			res.SuccessCount++

			// 直接写入有效手机号
			writer.WriteString(mobile + "\n")

			batchCount++
			if batchCount >= flushBatchSize {
				writer.Flush() // 定期刷新缓冲区
				batchCount = 0
			}
		}

		if err := scanner.Err(); err != nil {
			return nil, gerror.Wrap(err, "读取文件失败")
		}

		res.NewFileUrl = newFileUrl
	}

	return
}

// Cancel 取消发送
func (s *sSysDxSmsGroupSend) Cancel(ctx context.Context, in *sysin.DxSmsGroupSendCancelInp) (err error) {
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {
		var record *entity.DxSmsGroupSend
		if err = s.Model(ctx).Where("id", in.Id).Scan(&record); err != nil {
			err = gerror.Wrap(err, "获取短信群发记录失败")
			return
		}

		if record == nil {
			err = gerror.New("短信群发记录不存在")
			return
		}

		if record.SendStatus != 1 {
			err = gerror.New("只能取消等待发送状态的群发任务")
			return
		}

		if _, err = s.Model(ctx).
			Where("id", in.Id).
			Data(g.Map{
				"send_status": 6, // 设置为已取消状态
			}).Update(); err != nil {
			err = gerror.Wrap(err, "取消发送失败")
			return
		}

		return
	})
}
